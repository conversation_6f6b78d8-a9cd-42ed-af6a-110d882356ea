## Azure Container Registry Module
### v1.6.0 - June 27, 2025 [current]
ENHANCEMENTS:
- ❗Added the Network Rules Bypass option, which now defaults to **None** and accepts None or AzureServices as possible values - DDITSECCL-608

### v1.5.0 - Nov 21, 2024
ENHANCEMENTS:
- Updated alerting module version to support deployment in regions other than West Europe (optional variable can be used to override default behaviour: resource_health_alert_location)
- Updated pipelines to use v7 pipeline templates
- Release is tested with azurerm v4.10.0

### v1.4.1 - September 6, 2024
BUG FIXES:
- Corrected tag configuration

### v1.4.0 - Aug 29, 2024
ENHANCEMENTS:
- Minimum version azurerm v4.0.1
- encryption.enable variable removed as it is no longer supported by the resource
- retention_policy variable is removed and introduced a new variable to configure the same: retention_policy_in_days
- alerting version update to meet v4.x requirement
- DEV-coeinf.tfvars file updated and renamed to DEV-demo01.tfvars to use demo01 subscription for dev testing

### v1.3.0 - May 10, 2024
FEATURES:
- Zone redundancy is supported via new variable zone_redundancy_enabled. Changing this forces a new resource to be created. (Jira: CCE-6365)

ENHANCEMENTS:
- Remove azurerm and terraform upper limitation
- Updated pipelines as per new testing method

BUG FIXES:
- identity_ids parameter corrected to be optional

### v1.2.2 - Januar 30, 2024
- admin password expiration date handling change
### v1.2.1 - Januar 24, 2024 
- key gen change to module
### v1.2.0 - December 8, 2023
FEATURES:
- Encryption support enabled, JIRA ticket: CCE-4708
ENHANCEMENTS:
- Added tagging solution
- Udpated modules
- Removed locals from main.tf
- Added PR template
- Raised minimum provider version
### v1.1.0 - October 26, 2023
- Replaced logging solution to diagnostics module - Jira ticket: CCE-4421
- Updated module versions
- Maximum provider version is set to < 4.0.0 (tested on provider version 3.77.0)
### v1.0.0 - October 3, 2023
- Module prepared for release v1.0.0
- Updated module versions
- Updated provider versions
- Remove deprecated retention policy
### v0.4.3 - Set 8, 2023
- Alerting naming correction
### v0.4.2 - Aug 30, 2023
- [CCE-3849] Add alerting
### v0.4.1 - Aug 28, 2023 
- [CCE-3746] Externalize diagnosting logging settings
### v0.4.0 - July 7, 2023
- [CCE-3227] Add defender remediation
- Add logging
- [CCE-3227] disable export
- Add AcrPush permission for the executing user (Service Principal)
- Switch to Private endpoint v0.9.0
### v0.3.4 - June 16, 2023
- [CCE-3026] Fix module working if ACR disabled
### v0.3.3 - April 29, 2023
- [CCE-2584] AzureRM provider issue fixed. Buggy versions v3.52.0 and v3.53.0 excluded
### v0.3.2 - April 19, 2023
- Expose the private endpoint wait time
- Switch to private endpoint v0.6.0
- Repo restructuring (https://confluence.otpbank.hu/x/dVbwKw)
- [CCE-2584] Locked to AzureRM provider v3.51.0 (as latest) - interim workaround
  Probable cause: (https://github.com/hashicorp/terraform-provider-azurerm/issues/21440)
### v0.3.1 - April 05, 2023 - [deprecated], not compatible with azurerm provider v3.52.0 and above
- Added naming convention feature to ensure globally unique naming.
- Updated versions of included modules
- Added absolute source path to tests
### v0.3.0 [deprecated]
- Switch private endpoint module from v0.4.1 to v0.4.2
- Added exported user name and password location in key vault to output
- [CCE-1401] Added multiple identities with AcrPull permission (breaking change)