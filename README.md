## Name of the module
Azure Container Registry Module

Shortname: acre

Terraform resource: azurerm_container_registry

## Short description of the module
This Terraform module deploys an Azure Container Registry

## Detailed description on Confluence
[Azure Container Registry](https://confluence.otpbank.hu/x/RQvHJ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.0.1

## Release notes – changes in the current and previous versions
[ChangeLog.md](ChangeLog.md)

## Resources generated by the module
- Azure Container Registry
- Private Endpoint

