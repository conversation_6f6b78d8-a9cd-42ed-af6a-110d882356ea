<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Container Registry Module

Shortname: acre

Terraform resource: azurerm\_container\_registry

## Short description of the module
This Terraform module deploys an Azure Container Registry

## Detailed description on Confluence
[Azure Container Registry](https://confluence.otpbank.hu/x/RQvHJ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.0.1

## Release notes – changes in the current and previous versions
[ChangeLog.md](ChangeLog.md)

## Resources generated by the module
- Azure Container Registry
- Private Endpoint

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.0.1)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.0.1)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality" = "low"
  }
}

```

## Example for Azure Container Registry creation

```hcl

module "rg" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = "acrtest01"
}

module "acr" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.6.0"
  #checkov:skip=CKV_AZURE_167: Even if the retention is set checkov is not able to identify the configuration.
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = "acrtest01"
  resource_group_name  = module.rg.rgrp.name
  subnet_id            = data.azurerm_subnet.poc-subnet.id
  enable_quarantine    = true
  
  zone_redundancy_enabled  = true
  retention_policy_in_days = 7
  
  generate_admin_token   = true
  secret_expiration_date = "2025-10-05T00:00:00Z" #Maximum expiration offset time is 180 days. Only in dev it can be 365.
  key_vault_id           = data.azurerm_key_vault.acr-vault.id
 

  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics      = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["Engine"]
  //Example with all log types:
  log_analytics_diag_logs    = ["AllLogs"]
}




```


## Resources

The following resources are used by this module:

- [azurerm_container_registry.acre](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/container_registry) (resource)
- [azurerm_container_registry_token.acrt-admin](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/container_registry_token) (resource)
- [azurerm_container_registry_token_password.acrt-admin-password](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/container_registry_token_password) (resource)
- [azurerm_role_assignment.rass_acr_sp](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.rass_acr_umid](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_security_center_assessment.acre_asc_assessment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_assessment) (resource)
- [azurerm_security_center_assessment_policy.acre_asc_policy](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_assessment_policy) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: terraform-conventions module

Type: `any`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Resource Group name

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id)

Description: (Required) Subnet where private endpoint will be created

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_acr_enable"></a> [acr\_enable](#input\_acr\_enable)

Description: (Optional) Enable usage of own ACR for the cluster

Type: `bool`

Default: `true`

### <a name="input_acr_umid_principal_ids"></a> [acr\_umid\_principal\_ids](#input\_acr\_umid\_principal\_ids)

Description: (Optional) Principal Id list of the User Assigned Managed Identities granting AcrPull permission

Type: `list(any)`

Default: `[]`

### <a name="input_acre_tags"></a> [acre\_tags](#input\_acre\_tags)

Description: (Optional) Tags to apply to the Azure Container Registry

Type: `map(string)`

Default: `null`

### <a name="input_admin_token_user"></a> [admin\_token\_user](#input\_admin\_token\_user)

Description: (Optional) Admin token name - used as username while accessing ACR

Type: `string`

Default: `"admintoken"`

### <a name="input_alert_StorageUsed_threshold"></a> [alert\_StorageUsed\_threshold](#input\_alert\_StorageUsed\_threshold)

Description: (Optional) Threshold for StorageUsed alert rule.

Type: `number`

Default: `483183820800`

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: (Optional) Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_enable_quarantine"></a> [enable\_quarantine](#input\_enable\_quarantine)

Description: (Optional) Enable image quarantine mode. Note, image tagging is not possible in quarantine mode.

Type: `bool`

Default: `false`

### <a name="input_encryption"></a> [encryption](#input\_encryption)

Description: The encryption block supports the following:  
  key\_vault\_key\_id - (Required) The ID of the Key Vault Key.  
  identity\_client\_id - (Required) The client ID of the managed identity associated with the encryption key.

Type:

```hcl
object({
    key_vault_key_id   = string
    identity_client_id = string
  })
```

Default: `null`

### <a name="input_generate_admin_token"></a> [generate\_admin\_token](#input\_generate\_admin\_token)

Description: (Optional) Generate and export admin access token to key vault

Type: `bool`

Default: `false`

### <a name="input_identity"></a> [identity](#input\_identity)

Description: (Optional)An identity block supports the following:  
  type - (Required) Specifies the type of Managed Service Identity that should be configured on this ACR. Possible values are SystemAssigned, UserAssigned and SystemAssigned, UserAssigned (to enable both).  
  identity\_ids - (Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this ACR.

Type:

```hcl
object({
    type         = string
    identity_ids = optional(list(string))
  })
```

Default: `null`

### <a name="input_key_vault_id"></a> [key\_vault\_id](#input\_key\_vault\_id)

Description: (Optional) Key Vault id for admin access token

Type: `string`

Default: `null`

### <a name="input_log_analytics_diag_logs"></a> [log\_analytics\_diag\_logs](#input\_log\_analytics\_diag\_logs)

Description: (Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_metrics"></a> [log\_analytics\_metrics](#input\_log\_analytics\_metrics)

Description: (Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace

Type: `string`

Default: `null`

### <a name="input_network_rule_bypass_option"></a> [network\_rule\_bypass\_option](#input\_network\_rule\_bypass\_option)

Description: (Optional) Tells what traffic can bypass network rules. This can be AzureServices or None.

Type: `string`

Default: `"None"`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_retention_policy_in_days"></a> [retention\_policy\_in\_days](#input\_retention\_policy\_in\_days)

Description: (Optional) The number of days to retain and untagged manifest after which it gets purged. Defaults to 7.

Type: `number`

Default: `7`

### <a name="input_secret_expiration_date"></a> [secret\_expiration\_date](#input\_secret\_expiration\_date)

Description: (Optional) Expiration date of the Kubernetes secrets in RFC3339 format. Stored in the key vault. Maximum expiration offset time is 180 days. Only in dev it can be 365.

Type: `string`

Default: `null`

### <a name="input_wait_after"></a> [wait\_after](#input\_wait\_after)

Description: (Optional) Seconds to wait after private endpoint created

Type: `number`

Default: `0`

### <a name="input_zone_redundancy_enabled"></a> [zone\_redundancy\_enabled](#input\_zone\_redundancy\_enabled)

Description: (Optional) Whether zone redundancy is enabled for this Container Registry? Changing this forces a new resource to be created. Defaults to false.

Type: `bool`

Default: `false`

## Outputs

The following outputs are exported:

### <a name="output_acre"></a> [acre](#output\_acre)

Description: n/a

### <a name="output_acre-password-vault-id"></a> [acre-password-vault-id](#output\_acre-password-vault-id)

Description: n/a

### <a name="output_acre-user"></a> [acre-user](#output\_acre-user)

Description: n/a

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->