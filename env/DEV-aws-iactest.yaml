# variables:
#   environment: EDV
#   appCode: aws-terratest
#   awsServiceConnectionName: EDV-AWS-IACTEST-SERVICE-CONNECTION
#   awsRegion: eu-west-1
#   awsS3BucketName: otp-diszi-iactest-tfstate-edv-01
#   awsS3BucketRegion: eu-west-1
#   awsDynamoDBTableName: otp-diszi-iactest-tflock-edv-01

variables:
  appCode: demo
  target: OTP-ADO-demo01-sub-dev-01
  armServiceConnectionName: DEV-OTP-ADO-demo01-sub-dev-01-sp-01-FED
  storageAccountContainerName: tfstate
  storageAccountName: stacweudevdevd000001
  storageAccountResourceGroup: rgrp-weu-dev-demo01-01
  keyVaultServiceConnectionName: DEV-OTP-ADO-demo01-sub-dev-01-sp-01-FED
  KeyVaultName: kvau-weu-dev-DEVD000001
  keyVaultCommaSeparatedSecretNames: sbb-pat