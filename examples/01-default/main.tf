
module "rg" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = "acrtest01"
}

module "acr" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.6.0"
  #checkov:skip=CKV_AZURE_167: Even if the retention is set checkov is not able to identify the configuration.
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = "acrtest01"
  resource_group_name  = module.rg.rgrp.name
  subnet_id            = data.azurerm_subnet.poc-subnet.id
  enable_quarantine    = true
  
  zone_redundancy_enabled  = true
  retention_policy_in_days = 7
  
  generate_admin_token   = true
  secret_expiration_date = timeadd(timestamp(), "1464h") # 61 days (61 * 24 = 1464 hours)
  key_vault_id           = data.azurerm_key_vault.acr-vault.id
  custom_keyvault_secret_name = random_string.random_kvs_name.result

  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics      = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["Engine"]
  //Example with all log types:
  log_analytics_diag_logs    = ["AllLogs"]
}

resource "random_string" "random_kvs_name" {
  length           = 8
  special          = false
}



