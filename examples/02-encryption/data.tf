data "azurerm_subnet" "poc-subnet" {
  name                 = var.subnet_name
  virtual_network_name = var.vnet_name
  resource_group_name  = var.vnet_rgrp_name
}

data "azurerm_key_vault" "acr-vault" {
  name                = var.kv_name
  resource_group_name = var.kv_rgrp_name
}

data "azurerm_key_vault" "shared" { //This is the key-vault for encryption key
  name                = var.kv_name
  resource_group_name = var.kv_rgrp_name
}
