locals {
  suffix = "acrtest02"
}

module "rg" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions = module.conventions
  resource_name_suffix = local.suffix
}


resource "azurerm_user_assigned_identity" "acr-uaid" {
  resource_group_name = module.rg.rgrp.name
  location            = module.conventions.region

  name = "acr-uaid${local.suffix}"
}

module "acrkey" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.4.0"
  conventions      = module.conventions
  key_name         = local.suffix
  key_vault_id     = data.azurerm_key_vault.shared.id
  key_type         = "RSA"
  key_size         = 4096
  key_opts         = ["decrypt","encrypt","sign","unwrapKey","verify","wrapKey"]
  expiration_date = "2025-10-01T19:00:00Z"

  rotation_policy = {
    expire_after = "P5M"
    notify_before_expiry = "P7D"
    automatic = {
      automatic_time_before_expiry = "P10D" //https://en.wikipedia.org/wiki/ISO_8601#Durations
    }
  }
}

resource "azurerm_role_assignment" "acr-encryption-key" {
  role_definition_name = "Key Vault Crypto User"
  scope              = data.azurerm_key_vault.shared.id
  principal_id       = azurerm_user_assigned_identity.acr-uaid.principal_id
}

module "acr" {
  #checkov:skip=CKV_AZURE_167: Even if the retention is set checkov is not able to identify the configuration.
  # source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.6.0"
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  resource_group_name  = module.rg.rgrp.name
  subnet_id            = data.azurerm_subnet.poc-subnet.id
  enable_quarantine    = true

  generate_admin_token   = true
  secret_expiration_date = "2025-10-26T00:00:00Z" #Maximum expiration offset time is 180 days. Only in dev it can be 365.
  key_vault_id           = data.azurerm_key_vault.acr-vault.id

  retention_policy_in_days = 7

  identity = {
    type = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.acr-uaid.id]
  }

  encryption = {
    enabled             = true
    key_vault_key_id    = module.acrkey.azurerm_key_vault_key.id
    identity_client_id  = azurerm_user_assigned_identity.acr-uaid.client_id

  }
  
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics      = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["Engine"]
  //Example with all log types:
  log_analytics_diag_logs    = ["AllLogs"]


  depends_on = [ azurerm_role_assignment.acr-encryption-key ]

}


