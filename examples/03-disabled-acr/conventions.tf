module "conventions" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality" = "low"
  }
}