
module "rg" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = "acrtest03"
}

module "acr" {
  #checkov:skip=CKV_AZURE_167: Even if the retention is set checkov is not able to identify the configuration.
  # source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.6.0"
  source               = "../.."
  conventions          = module.conventions
  resource_group_name  = module.rg.rgrp.name
  resource_name_suffix = "acrtest03"
  subnet_id            = data.azurerm_subnet.poc-subnet.id
  acr_enable           = false
  
  retention_policy_in_days = 7
  
  generate_admin_token   = true
  secret_expiration_date = "2025-10-26T00:00:00Z" #Maximum expiration offset time is 180 days. Only in dev it can be 365.
  key_vault_id           = data.azurerm_key_vault.acr-vault.id
  
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics      = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["Engine"]
  //Example with all log types:
  log_analytics_diag_logs    = ["AllLogs"]
}



