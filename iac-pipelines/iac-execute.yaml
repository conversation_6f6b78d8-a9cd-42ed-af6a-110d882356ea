parameters:
  # Common
  - name: cloud
    default: azure
    values:
      - azure
      - gcp
  - name: action
  - name: environment
  - name: appCode
  - name: timeoutInMinutes

  # Azure Auth
  - name: armServiceConnectionName
    default: ''
  - name: sasTokenLifetimeMinutes
    default: 540

  # GCP Auth
  - name: googleCloudProject
    default: ''
  - name: googleCloudRegion
    default: ''
  - name: googleCloudKeyFile
    default: ''

  # Azure Backend
  - name: storageAccountResourceGroup
    default: ''
  - name: storageAccountName
    default: ''
  - name: storageAccountContainerName
    default: ''
  - name: terraformStateFileSnapshot
    default: true
  - name: storageAccountUseAzureadAuth
    default: true

  # GCP Backend
  - name: googleBucketName
    default: ''
  - name: googleImpersonateServiceAccount
    default: ''

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
    default: ''
  - name: terraformVersion
    default: '1.9.8'
  - name: terraformExtraNoProxy
    default: ''
  - name: terraformRCFileForNetworkMirror
    default: '$(Pipeline.Workspace)/tooling_repo/.config/.terraformrc'
  - name: terraformUnlockStateLockID
    default: ' '
  - name: terraformLogLevel
    default: 'ERROR'

  # Terraform CLI options
  # Init
  - name: terraformCLIGlobalOptionsForInit
    default: ''
  - name: terraformCLIOptionsForInit
    default: ''
  # Plan
  - name: terraformCLIGlobalOptionsForPlan
    default: ''
  - name: terraformCLIOptionsForPlan
    default: ''
  # Apply
  - name: terraformCLIGlobalOptionsForApply
    default: ''
  - name: terraformCLIOptionsForApply
    default: ''

  # Checkov parameters
  - name: skipCheckovScan
  - name: checkovExtraParams
    default: ''
  - name: terraformOutputVariablesToPipelineVariables
    type: object
    default: {}

extends:
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/pipeline-generic.yaml
  template: iac-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    agentPoolNamePostfix: "AksPool-centralagent-Deploy"
    environment: ${{ split(parameters.environment, '-')[0] }}
    appCode: ${{ parameters.appcode }}
    # pipelinetemplateRepoReference: pipelinetemplates
    pipelinetemplateRepoReference: self
    sonarQubeScanIsNotApplicableForThisApplication: true
    # configRepoReference: self

###############################################
#### Initialization Stage - Terraform Plan ####
###############################################
    initializationStageJobTimeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    ### init -1 - keyvault
    # keyVaultServiceConnectionName:
    # KeyVaultName:
    # keyVaultCommaSeparatedSecretNames:
    # keyVaultRunAsPreJob:

    ### init 0
    # stepsPreInitializationStage:

    ### init 1
    stepsInitialization:

#  $$$$$$\  $$$$$$$$\ $$\   $$\ $$$$$$$\  $$$$$$$$\       $$$$$$$\  $$\        $$$$$$\  $$\   $$\ 
# $$  __$$\ \____$$  |$$ |  $$ |$$  __$$\ $$  _____|      $$  __$$\ $$ |      $$  __$$\ $$$\  $$ |
# $$ /  $$ |    $$  / $$ |  $$ |$$ |  $$ |$$ |            $$ |  $$ |$$ |      $$ /  $$ |$$$$\ $$ |
# $$$$$$$$ |   $$  /  $$ |  $$ |$$$$$$$  |$$$$$\          $$$$$$$  |$$ |      $$$$$$$$ |$$ $$\$$ |
# $$  __$$ |  $$  /   $$ |  $$ |$$  __$$< $$  __|         $$  ____/ $$ |      $$  __$$ |$$ \$$$$ |
# $$ |  $$ | $$  /    $$ |  $$ |$$ |  $$ |$$ |            $$ |      $$ |      $$ |  $$ |$$ |\$$$ |
# $$ |  $$ |$$$$$$$$\ \$$$$$$  |$$ |  $$ |$$$$$$$$\       $$ |      $$$$$$$$\ $$ |  $$ |$$ | \$$ |
# \__|  \__|\________| \______/ \__|  \__|\________|      \__|      \________|\__|  \__|\__|  \__|

      - ${{ if eq(parameters.cloud, 'azure') }}:
        #### Terraform init steps for Azure
        - template: iac-execute-step-init.yaml
          parameters:
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
            sasTokenLifetimeMinutes: ${{ parameters.sasTokenLifetimeMinutes }}

            #Azure Backend
            storageAccountResourceGroup: ${{ parameters.storageAccountResourceGroup }}
            storageAccountName: ${{ parameters.storageAccountName }}
            storageAccountContainerName: ${{ parameters.storageAccountContainerName }}
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
            terraformStateFileSnapshot: ${{ parameters.terraformStateFileSnapshot }}
            storageAccountUseAzureadAuth: ${{ parameters.storageAccountUseAzureadAuth }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

            # Not exposed options
            publishArtifact: true

        #### Terraform plan steps for Azure
        - template: iac-execute-step-plan.yaml
          parameters:
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}

            # Common
            environment: ${{ parameters.environment }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
            terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}

            # Not exposed options
            ${{ if eq(parameters.action, 'destroy') }}:
              terraformDestroyPlan: true
            tfvarsToUseFromPipelineVariables: 
              - tfvarName: owner
                varName: $(Build.QueuedBy)
              - tfvarName: owner_dl
                varName: <EMAIL>

#  $$$$$$\   $$$$$$\  $$$$$$$\        $$$$$$$\  $$\        $$$$$$\  $$\   $$\ 
# $$  __$$\ $$  __$$\ $$  __$$\       $$  __$$\ $$ |      $$  __$$\ $$$\  $$ |
# $$ /  \__|$$ /  \__|$$ |  $$ |      $$ |  $$ |$$ |      $$ /  $$ |$$$$\ $$ |
# $$ |$$$$\ $$ |      $$$$$$$  |      $$$$$$$  |$$ |      $$$$$$$$ |$$ $$\$$ |
# $$ |\_$$ |$$ |      $$  ____/       $$  ____/ $$ |      $$  __$$ |$$ \$$$$ |
# $$ |  $$ |$$ |  $$\ $$ |            $$ |      $$ |      $$ |  $$ |$$ |\$$$ |
# \$$$$$$  |\$$$$$$  |$$ |            $$ |      $$$$$$$$\ $$ |  $$ |$$ | \$$ |
#  \______/  \______/ \__|            \__|      \________|\__|  \__|\__|  \__|

      - ${{ if eq(parameters.cloud, 'gcp') }}:
        #### Terraform init steps for GCP
        - template: iac-execute-step-init-gcp.yaml
          parameters:
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

            # Not exposed options
            publishArtifact: true

        #### Terraform plan steps for GCP
        - template: iac-execute-step-plan-gcp.yaml
          parameters:
            # Common
            environment: ${{ parameters.environment }}
            terraformGlobalTFVars: ${{ parameters.terraformGlobalTFVars }}

            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
            terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}

            # Not exposed options
            ${{ if eq(parameters.action, 'destroy') }}:
              terraformDestroyPlan: true
            tfvarsToUseFromPipelineVariables:
              - tfvarName: owner
                varName: $(Build.QueuedBy)
              - tfvarName: owner_dl
                varName: <EMAIL>

        - bash: |
            pip install checkov==3.2.400
            checkov -d . --framework terraform --skip-path examples
          displayName: "Checkovscan"

    ### init 2
    # stepsPostInitialization:

    ## init 3
    # Checkov scan
    skipCheckovScan: ${{ parameters.skipCheckovScan }}
    # checkovScanFailPipeline: ${{ parameters.skipCheckovScan }}

    ${{ if eq(parameters.checkovExtraParams, '') }}:
      checkovExtraParams: -d "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}" --var-file "$(Pipeline.Workspace)/config_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"
    ${{ else }}:
      checkovExtraParams: ${{ parameters.checkovExtraParams }}

    ### init 4
    # stepsPostInitializationStage:

    ### init 5
    # agentlessStepsPostInitializationStageJobTimeoutInMinutes:
    # agentlessStepsPostInitializationStageJobCondition:
    # agentlessStepsPostInitializationStage:


############################################
#### Deployment Stage - Terraform Apply ####
############################################
    deploymentStageJobTimeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    ## Run Condition - 2 Way
    ${{ if eq(parameters.action, 'plan') }}:
      deploymentStageCondition: false
    ${{ else }}:
      deploymentStageCondition: and(succeeded(), eq(dependencies.Initialization.outputs['Initialization.plan.terraform_changes_to_apply'], 'true'))

    ## Run Condition - 3 Way
    # deploymentStageCondition: |
    #   and(
    #     ne('${{ parameters.action }}', 'plan'),
    #     or(
    #       eq('${{ parameters.action }}', 'test'),
    #       and(
    #         succeeded(),
    #         eq(dependencies.Initialization.outputs['Initialization.plan.terraform_changes_to_apply'], 'true')
    #       )
    #     )
    #   )

    ### deploy -1 - keyvault
    # keyVaultServiceConnectionName:
    # KeyVaultName:
    # keyVaultCommaSeparatedSecretNames:
    # keyVaultRunAsPreJob:

    ### deploy 0
    # stepsPreDeploymentStage:

    ### deploy 1
    stepsDeployment:

#  $$$$$$\  $$$$$$$$\ $$\   $$\ $$$$$$$\  $$$$$$$$\        $$$$$$\  $$$$$$$\  $$$$$$$\  $$\   $$\     $$\ 
# $$  __$$\ \____$$  |$$ |  $$ |$$  __$$\ $$  _____|      $$  __$$\ $$  __$$\ $$  __$$\ $$ |  \$$\   $$  |
# $$ /  $$ |    $$  / $$ |  $$ |$$ |  $$ |$$ |            $$ /  $$ |$$ |  $$ |$$ |  $$ |$$ |   \$$\ $$  / 
# $$$$$$$$ |   $$  /  $$ |  $$ |$$$$$$$  |$$$$$\          $$$$$$$$ |$$$$$$$  |$$$$$$$  |$$ |    \$$$$  /  
# $$  __$$ |  $$  /   $$ |  $$ |$$  __$$< $$  __|         $$  __$$ |$$  ____/ $$  ____/ $$ |     \$$  /   
# $$ |  $$ | $$  /    $$ |  $$ |$$ |  $$ |$$ |            $$ |  $$ |$$ |      $$ |      $$ |      $$ |    
# $$ |  $$ |$$$$$$$$\ \$$$$$$  |$$ |  $$ |$$$$$$$$\       $$ |  $$ |$$ |      $$ |      $$$$$$$$\ $$ |    
# \__|  \__|\________| \______/ \__|  \__|\________|      \__|  \__|\__|      \__|      \________|\__|    

      - ${{ if eq(parameters.cloud, 'azure') }}:
        ## Terraform init steps for Azure
        - template: iac-execute-step-init.yaml
          parameters:
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
            sasTokenLifetimeMinutes: ${{ parameters.sasTokenLifetimeMinutes }}

            #Azure Backend
            storageAccountResourceGroup: ${{ parameters.storageAccountResourceGroup }}
            storageAccountName: ${{ parameters.storageAccountName }}
            storageAccountContainerName: ${{ parameters.storageAccountContainerName }}
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
            terraformStateFileSnapshot: ${{ parameters.terraformStateFileSnapshot }}
            storageAccountUseAzureadAuth: ${{ parameters.storageAccountUseAzureadAuth }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

        ## Terraform apply steps for Azure
        - template: iac-execute-step-apply.yaml
          parameters:
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
            terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}
            #terraformLogLevel: ${{ parameters.terraformLogLevel }}

#  $$$$$$\   $$$$$$\  $$$$$$$\         $$$$$$\  $$$$$$$\  $$$$$$$\  $$\   $$\     $$\ 
# $$  __$$\ $$  __$$\ $$  __$$\       $$  __$$\ $$  __$$\ $$  __$$\ $$ |  \$$\   $$  |
# $$ /  \__|$$ /  \__|$$ |  $$ |      $$ /  $$ |$$ |  $$ |$$ |  $$ |$$ |   \$$\ $$  / 
# $$ |$$$$\ $$ |      $$$$$$$  |      $$$$$$$$ |$$$$$$$  |$$$$$$$  |$$ |    \$$$$  /  
# $$ |\_$$ |$$ |      $$  ____/       $$  __$$ |$$  ____/ $$  ____/ $$ |     \$$  /   
# $$ |  $$ |$$ |  $$\ $$ |            $$ |  $$ |$$ |      $$ |      $$ |      $$ |    
# \$$$$$$  |\$$$$$$  |$$ |            $$ |  $$ |$$ |      $$ |      $$$$$$$$\ $$ |    
#  \______/  \______/ \__|            \__|  \__|\__|      \__|      \________|\__|    

      - ${{ if eq(parameters.cloud, 'gcp') }}:
        #### Terraform init steps for GCP
        - template: iac-execute-step-init-gcp.yaml
          parameters:
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

        #### Terraform apply steps for GCP
        - template: iac-execute-step-apply-gcp.yaml
          parameters:
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
            terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}

      ## Post apply steps --> move to stepsPostDeployment?
      - ${{ each item in parameters.terraformOutputVariablesToPipelineVariables }}:
        - template: iac-common/step-tf-output-to-pipeline-var.yaml@pipelinetemplates
          parameters:
            terraformOutputVariableName: ${{ item.terraformVariableName }}
            pipelineVariableName: ${{ item.pipelineVariableName }}
            pipelineVariableIsSecret: ${{ item.pipelineVariableIsSecret }}
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}

    ############# Steps for TEST function ############
    stepsPostDeployment:

#  $$$$$$\  $$$$$$$$\ $$\   $$\ $$$$$$$\  $$$$$$$$\       $$$$$$$$\ $$$$$$$$\  $$$$$$\ $$$$$$$$\ 
# $$  __$$\ \____$$  |$$ |  $$ |$$  __$$\ $$  _____|      \__$$  __|$$  _____|$$  __$$\\__$$  __|
# $$ /  $$ |    $$  / $$ |  $$ |$$ |  $$ |$$ |               $$ |   $$ |      $$ /  \__|  $$ |   
# $$$$$$$$ |   $$  /  $$ |  $$ |$$$$$$$  |$$$$$\             $$ |   $$$$$\    \$$$$$$\    $$ |   
# $$  __$$ |  $$  /   $$ |  $$ |$$  __$$< $$  __|            $$ |   $$  __|    \____$$\   $$ |   
# $$ |  $$ | $$  /    $$ |  $$ |$$ |  $$ |$$ |               $$ |   $$ |      $$\   $$ |  $$ |   
# $$ |  $$ |$$$$$$$$\ \$$$$$$  |$$ |  $$ |$$$$$$$$\          $$ |   $$$$$$$$\ \$$$$$$  |  $$ |   
# \__|  \__|\________| \______/ \__|  \__|\________|         \__|   \________| \______/   \__|   

      - ${{ if and(eq(parameters.action, 'test'), eq(parameters.cloud, 'azure')) }}:
        - template: iac-execute-step-serviceconnection-login.yaml
          parameters:
            armServiceConnection: ${{ parameters.armServiceConnectionName }}

        ## Plan for destroy
        - template: iac-execute-step-plan.yaml
          parameters:
            taskcondition: always()
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
            # Common
            environment: ${{ parameters.environment }}
            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}
            # Terraform CLI options
            terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
            terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}
            terraformDestroyPlan: true

            planFilePath: tfplandestroy.out
            artifactname: tfplandestroy

            tfvarsToUseFromPipelineVariables: 
              - tfvarName: owner
                varName: $(Build.QueuedBy)
              - tfvarName: owner_dl
                varName: <EMAIL>

        ## Execute destroy
        - template: iac-execute-step-apply.yaml
          parameters:
            taskcondition: succeededOrFailed()
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}
            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            # Terraform CLI options
            terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
            terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}
            #terraformLogLevel: ${{ parameters.terraformLogLevel }}

            planFilePath: tfplandestroy.out
            artifactname: tfplandestroy

#  $$$$$$\   $$$$$$\  $$$$$$$\        $$$$$$$$\ $$$$$$$$\  $$$$$$\ $$$$$$$$\ 
# $$  __$$\ $$  __$$\ $$  __$$\       \__$$  __|$$  _____|$$  __$$\\__$$  __|
# $$ /  \__|$$ /  \__|$$ |  $$ |         $$ |   $$ |      $$ /  \__|  $$ |   
# $$ |$$$$\ $$ |      $$$$$$$  |         $$ |   $$$$$\    \$$$$$$\    $$ |   
# $$ |\_$$ |$$ |      $$  ____/          $$ |   $$  __|    \____$$\   $$ |   
# $$ |  $$ |$$ |  $$\ $$ |               $$ |   $$ |      $$\   $$ |  $$ |   
# \$$$$$$  |\$$$$$$  |$$ |               $$ |   $$$$$$$$\ \$$$$$$  |  $$ |   
#  \______/  \______/ \__|               \__|   \________| \______/   \__|   

      - ${{ if and(eq(parameters.action, 'test'), eq(parameters.cloud, 'gcp')) }}:
        ## Plan for destroy
        - template: iac-execute-step-plan-gcp.yaml
          parameters:
            taskcondition: always()
            # Common
            environment: ${{ parameters.environment }}
            terraformGlobalTFVars: ${{ parameters.terraformGlobalTFVars }}

            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
            terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}
            terraformDestroyPlan: true

            planFilePath: tfplandestroy.out
            artifactname: tfplandestroy

            tfvarsToUseFromPipelineVariables: 
              - tfvarName: owner
                varName: $(Build.QueuedBy)
              - tfvarName: owner_dl
                varName: <EMAIL>

        ## Execute destroy
        - template: iac-execute-step-apply-gcp.yaml
          parameters:
            taskcondition: succeededOrFailed()
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformLogLevel: ${{ parameters.terraformLogLevel }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
            terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}

            planFilePath: tfplandestroy.out
            artifactname: tfplandestroy

      - ${{ each item in parameters.terraformOutputVariablesToPipelineVariables }}:
        - template: iac-common/step-tf-output-to-pipeline-var.yaml@pipelinetemplates
          parameters:
            terraformOutputVariableName: ${{ item.terraformVariableName }}
            pipelineVariableName: ${{ item.pipelineVariableName }}
            pipelineVariableIsSecret: ${{ item.pipelineVariableIsSecret }}
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}

    stepsPostDeploymentStage:
      ## Cleanup in case of failure
      - ${{ if and(or(eq(parameters.action, 'test'), eq(parameters.action, 'destroy')), eq(parameters.cloud, 'azure')) }}:
        - template: iac-execute-step-cleanup.yaml
          parameters:
            # Auth
            armServiceConnectionName: ${{ parameters.armServiceConnectionName }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}

            storageAccountName: ${{ parameters.storageAccountName }}
            storageAccountContainerName: ${{ parameters.storageAccountContainerName }}
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}