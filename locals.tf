locals {
  //Tags
  tags = merge(
    var.conventions.tags,
    var.acre_tags,
    {
      creation_mode         = "terraform",
      terraform-azurerm-acr = "v1.6.0",
    }
  )
  //Names
  resource_shortname = "acre"
  nc_hyphen          = "${var.conventions.short_region}-${var.conventions.environment}-${var.conventions.project}-${var.resource_name_suffix}"
  nc_no_hyphen       = replace(local.nc_hyphen, "-", "")
}