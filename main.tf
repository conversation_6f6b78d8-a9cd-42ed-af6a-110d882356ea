// Create the registry itself
resource "azurerm_container_registry" "acre" {
  // Checkov
  #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
  #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
  #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
  count                         = var.acr_enable ? 1 : 0
  name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
  resource_group_name           = var.resource_group_name
  location                      = var.conventions.region
  sku                           = "Premium"
  public_network_access_enabled = false
  zone_redundancy_enabled       = var.zone_redundancy_enabled
  retention_policy_in_days      = var.retention_policy_in_days

  export_policy_enabled     = false
  tags                      = local.tags
  quarantine_policy_enabled = var.enable_quarantine

  network_rule_bypass_option = var.network_rule_bypass_option

  dynamic "identity" {
    for_each = var.identity == null ? [] : [1]
    content {
      type         = var.identity.type
      identity_ids = var.identity.identity_ids
    }
  }

  //dynamic "georeplications"

  dynamic "encryption" {
    for_each = var.encryption == null ? [] : [1]
    content {
      key_vault_key_id   = var.encryption.key_vault_key_id
      identity_client_id = var.encryption.identity_client_id
    }
  }

}

// Access token generation and publishing
data "azurerm_container_registry_scope_map" "acrs-system-admin" {
  count                   = var.generate_admin_token && var.acr_enable ? 1 : 0
  name                    = "_repositories_admin"
  resource_group_name     = var.resource_group_name
  container_registry_name = azurerm_container_registry.acre[0].name
}

resource "azurerm_container_registry_token" "acrt-admin" {
  count                   = var.generate_admin_token && var.acr_enable ? 1 : 0
  name                    = var.admin_token_user
  container_registry_name = azurerm_container_registry.acre[0].name
  resource_group_name     = var.resource_group_name
  scope_map_id            = data.azurerm_container_registry_scope_map.acrs-system-admin[0].id
}

resource "azurerm_container_registry_token_password" "acrt-admin-password" {
  count                       = var.generate_admin_token && var.acr_enable ? 1 : 0
  container_registry_token_id = azurerm_container_registry_token.acrt-admin[0].id

  password1 {
    expiry = var.secret_expiration_date
  }
}

module "keyvaultsecret01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  count           = var.generate_admin_token && var.acr_enable ? 1 : 0
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-secret?ref=v1.4.0"
  conventions     = var.conventions
  name            = var.custom_keyvault_secret_name == null ? "${azurerm_container_registry.acre[0].name}-${azurerm_container_registry_token.acrt-admin[0].name}" : "${azurerm_container_registry.acre[0].name}-${azurerm_container_registry_token.acrt-admin[0].name}-${var.custom_keyvault_secret_name}"
  value           = azurerm_container_registry_token_password.acrt-admin-password[0].password1[0].value
  key_vault_id    = var.key_vault_id
  expiration_date = var.secret_expiration_date
}

// Private endpoint
module "prep" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-private-endpoint?ref=v1.3.0"
  count                = var.acr_enable ? 1 : 0
  conventions          = var.conventions
  resource_group_name  = var.resource_group_name
  location             = var.conventions.region
  resource_name_suffix = "${var.resource_name_suffix}-${local.resource_shortname}01"
  subnet_id            = var.subnet_id
  resource_id          = azurerm_container_registry.acre[0].id
  subresource_list     = ["registry"]
  wait_after           = var.wait_after
}

resource "azurerm_role_assignment" "rass_acr_umid" {
  count                = var.acr_enable ? length(var.acr_umid_principal_ids) : 0
  principal_id         = var.acr_umid_principal_ids[count.index]
  role_definition_name = "AcrPull"
  scope                = azurerm_container_registry.acre[0].id
}

// Service principal permission
data "azurerm_client_config" "current" {}

resource "azurerm_role_assignment" "rass_acr_sp" {
  count                = var.acr_enable ? 1 : 0
  principal_id         = data.azurerm_client_config.current.object_id
  role_definition_name = "AcrPush"
  scope                = azurerm_container_registry.acre[0].id
}


// Security Center
resource "azurerm_security_center_assessment_policy" "acre_asc_policy" {
  count        = var.acr_enable ? 1 : 0
  display_name = "${local.resource_shortname}-${local.nc_hyphen}"
  severity     = "Medium"
  description  = "ACR Policy"
}

resource "azurerm_security_center_assessment" "acre_asc_assessment" {
  count                = var.acr_enable ? 1 : 0
  assessment_policy_id = azurerm_security_center_assessment_policy.acre_asc_policy[0].id
  target_resource_id   = azurerm_container_registry.acre[0].id

  status {
    code = "Healthy"
  }
}

// Logging
module "acr_logging" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  count                          = var.acr_enable && (length(var.log_analytics_metrics) != 0 || length(var.log_analytics_diag_logs) != 0) ? 1 : 0
  source                         = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-diag?ref=v1.1.0"
  resource_name_suffix           = var.resource_name_suffix
  conventions                    = var.conventions
  diag_target_resource_id        = azurerm_container_registry.acre[0].id
  diag_loganalytics_workspace_id = var.log_analytics_workspace_id
  diag_loganalytics_diag_logs    = var.log_analytics_diag_logs
  diag_loganalytics_metrics      = var.log_analytics_metrics
}


//----------------------------------------------------------------- Resource health alert
module "resource_health_acre" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring && var.acr_enable ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5.0"
  resource_health_alert = true
  conventions           = var.conventions
  resource_group_name   = var.resource_group_name
  resource_name_suffix  = "acre-resourcehealth-${var.resource_name_suffix}"
  alert_location        = var.resource_health_alert_location

  scopes = [resource.azurerm_container_registry.acre[0].id]
}

// --------------------------------------------------------------- Default metric alerts
module "builtin_metrics_acre" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  count               = var.builtin_metric_monitoring && var.acr_enable ? 1 : 0
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//metric_alert?ref=v1.5.0"
  conventions         = var.conventions
  resource_group_name = var.resource_group_name


  metric_alerts = {
    "StorageUsed" = {
      resource_name_suffix = "acre-StorageUsed-${var.resource_name_suffix}"
      scopes               = [resource.azurerm_container_registry.acre[0].id]
      description          = "Storage Utilization"
      severity             = 1
      frequency            = "PT5M"
      window_size          = "PT1H"

      criteria = [
        {
          metric_namespace = "Microsoft.ContainerRegistry/registries"
          metric_name      = "StorageUsed"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_StorageUsed_threshold //default 450 - The Premium SKU can hold 500GB images
        }
      ]
    }
  }
}  