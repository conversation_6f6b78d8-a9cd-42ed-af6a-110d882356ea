#!/bin/bash

az devops configure --defaults organization=https://dev.azure.com/ADOS-OTPHU-01
az devops configure --defaults project=OTPHU-COE-TEMPLATESPEC

# Check for --tags parameter
include_tags=false
if [[ "$1" == "--tags" ]]; then
    include_tags=true
fi

# Get relevant repos
repos=$(az repos list -o json | jq -r '.[] | select(.name | startswith("terraform-azurerm") or .name | startswith("terraform-google")) | .name')

# Get Azure pipelines (terraformtest pattern)
azure_pipelines=$(az pipelines list --query [].name -o tsv | grep -E '^terraform-azurerm[a-zA-Z_\-]+[Tt]erraform[a-zA-Z_\-]+(TST|PRD).*|^terraform-azurerm[a-zA-Z_\-]+[Cc]heckov$')

# Get Google pipelines (tftest pattern)
google_pipelines=$(az pipelines list --query [].name -o tsv | grep -E '^terraform-google[a-zA-Z_\-]+tftest[a-zA-Z_\-]+(TST|PRD).*|^terraform-google[a-zA-Z_\-]+[Cc]heckov$') #EDV|STT|RPR|RPD|

# Combine all pipelines
all_pipelines=$(printf "%s\n%s" "$azure_pipelines" "$google_pipelines" | grep -v '^$')

# Define pipelines to exclude from processing
exclude_pipelines=("Oracle" "terraform-azurerm-opdg" "terraform-azurerm-virtual-desktop")

# Filter out excluded pipelines from all_pipelines
for exclude in "${exclude_pipelines[@]}"; do
    all_pipelines=$(echo "$all_pipelines" | grep -v "$exclude")
done

declare -A repo_tags repo_pipelines

for repo in $repos; do
    # Get sorted tags only if --tags is specified
    tags=""
    if $include_tags; then
        tags_sorted=$(az repos ref list --repository "$repo" --filter tags --query [].name -o tsv | grep -E '^refs/tags/v[0-9]+\.[0-9]+\.[0-9]+$' | grep -v '-' | sed 's|^refs/tags/||' | sort -Vr)

        # Get unique major
        majors=$(echo "$tags_sorted" | awk -F. '{print $1}' | sort -u)

        # Select latest minor,patch per major
        for major in $majors; do
            latest=$(echo "$tags_sorted" | grep "^${major}\." | head -1)
            if [[ -n "$latest" ]]; then
                tags+="$latest "
            fi
        done
    fi
    repo_tags["$repo"]="${tags% }"

    # Collect all pipelines (terraform tests + checkov) into one array
    # Handle different patterns for Azure (terraformtest) and Google (tftest) repos
    terraform_pipelines=$(echo "$all_pipelines" | grep -E "^$repo[_\-]([Tt]erraform|tftest)")
    checkov_pipelines=$(echo "$all_pipelines" | grep -E "^${repo}[_\-][Cc]heckov$")

    # Combine terraform and checkov pipelines
    all_repo_pipelines=""
    if [[ -n "$terraform_pipelines" ]]; then
        all_repo_pipelines="$terraform_pipelines"
    fi
    if [[ -n "$checkov_pipelines" ]]; then
        if [[ -n "$all_repo_pipelines" ]]; then
            all_repo_pipelines+=$'\n'"$checkov_pipelines"
        else
            all_repo_pipelines="$checkov_pipelines"
        fi
    fi

    repo_pipelines["$repo"]="$all_repo_pipelines"
done

# Generate JSON
json='{"repos": ['
first=true
for repo in $repos; do
    tags="${repo_tags[$repo]}"
    pipelines="${repo_pipelines[$repo]}"

    # Include only repos with pipelines (and tags if --tags is specified)
    if [[ -n "$pipelines" && ("$include_tags" == "false" || -n "$tags") ]]; then
        if ! $first; then json+=','; fi
        first=false
        json+='{"name": "'"$repo"'"'

        # Add tags only if --tags is specified AND tags is not just "main"
        if $include_tags && [[ -n "$tags" ]]; then
            json+=', "tags": ['
            tag_first=true
            for tag in $tags; do
                if ! $tag_first; then json+=','; fi
                tag_first=false
                json+='"'$tag'"'
            done
            json+=']'
        fi

        # Add pipelines array
        json+=', "pipelines": ['
        pl_first=true
        while IFS= read -r pl; do
            [[ -z "$pl" ]] && continue
            if ! $pl_first; then json+=','; fi
            pl_first=false
            json+='"'$pl'"'
        done <<< "$pipelines"
        json+=']'

        json+='}'
    fi
done
json+=']}'

# Format and save JSON
echo "$json" | jq . > config.json