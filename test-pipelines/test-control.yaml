trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • Terraform Tests

variables:
  - group: TEST

jobs:
- job: Control
  timeoutInMinutes: 600  # 10 hours maximum
  pool:
    name: DEV-AksPool-centralagent-Deploy
  steps:
    - checkout: self
      fetchDepth: 0
    - task: AzureCLI@2
      displayName: Control terraform test pipelines
      env:
        AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
      inputs:
        azureSubscription: DEV-OTP-DD-COEINFDEV-sub-dev-01-FED
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          az devops configure --defaults organization=https://dev.azure.com/ADOS-OTPHU-01
          az devops configure --defaults project=OTPHU-COE-TEMPLATESPEC

          config_file=$(Build.SourcesDirectory)/test-pipelines/config.json
          repos=$(jq -r '.repos[].name' $config_file)

          declare -A repo_tags repo_pipelines result_files
          declare -a pids

          result_dir="result"
          mkdir $result_dir
          max_parallel_jobs=20

          for repo in $repos; do
            tags=$(jq -r --arg r "$repo" '.repos[] | select(.name == $r) | (.tags // ["main"])[]' $config_file)
            repo_tags["$repo"]="$tags"

            pipelines=$(jq -r --arg r "$repo" '.repos[] | select(.name == $r) | .pipelines[]' $config_file)
            repo_pipelines["$repo"]="$pipelines"
          done

          echo "=== Repository Configuration ==="

          for repo in $repos; do
              [[ -z "$repo" ]] && continue
              echo "[INFO] Repo: $repo"

              tags="${repo_tags[$repo]:-}"
              echo "[INFO]   Tags:"
              while IFS= read -r tag; do
                  [[ -z "$tag" ]] && continue
                  echo "[INFO]     $tag"
              done <<< "$tags"

              pipelines="${repo_pipelines[$repo]:-}"
              if [[ -n "$pipelines" ]]; then
                  echo "[INFO]   Pipelines:"
                  while IFS= read -r pl; do
                      [[ -z "$pl" ]] && continue
                      echo "[INFO]     $pl"
                  done <<< "$pipelines"
              else
                  echo "[INFO]   Pipelines: (none)"
              fi
          done

          function wait_for_jobs {
            sleep 30s
            job_wait_counter=0
            max_job_wait=240  # 2 hours (240 * 30s)
            while [ $(jobs -rp | wc -l) -ge $max_parallel_jobs ] && [ $job_wait_counter -lt $max_job_wait ]; do
              ((job_wait_counter++))
              # echo "[INFO] Waiting for jobs to complete... Active jobs: $(jobs -rp | wc -l)/$max_parallel_jobs (wait: $job_wait_counter/$max_job_wait)"
              sleep 30s
            done

            if [ $job_wait_counter -ge $max_job_wait ]; then
              echo "##vso[task.logissue type=warning]Job wait timeout reached. Some background jobs may be stuck. Active job PIDs: $(jobs -rp | tr '\n' ' ')"
            fi
          }

          function process_repo {
            local repo=$1
            local tags="${repo_tags[$repo]}"
            local pipelines="${repo_pipelines[$repo]}"

            for tag in $tags; do
              branch=$([[ "$tag" == "main" ]] && echo "refs/heads/main" || echo "refs/tags/$tag")
              for pipeline in $pipelines; do
                wait_for_jobs

                # echo "[INFO] Attempting to start pipeline $pipeline"
                full_output=$(az pipelines run --name "$pipeline" --branch "$branch" --query "id" --output tsv 2>&1)
                if [[ $full_output =~ ^[0-9]+$ ]]; then
                  build_id=$full_output
                else
                  build_id=""
                fi

                key="${repo}#${tag}#${pipeline}"
                result_file="$result_dir/$key.result"
                result_files[$key]=$result_file

                if [[ -z "$build_id" ]]; then
                  echo "##vso[task.logissue type=warning]FAILED to trigger pipeline $pipeline for $repo/$tag. Output: $full_output"
                  echo "failed_to_trigger;failed" > $result_file
                  continue
                fi

                echo "[INFO] Started $repo/$tag/$pipeline: $build_id"

                status=""
                timeout_counter=0
                max_timeout=120  # 2 hours (120 * 60s)
                while [[ $status != "completed" ]] && [[ $timeout_counter -lt $max_timeout ]]; do
                  sleep 60s
                  ((timeout_counter++))
                  status=$(az pipelines runs show --id "$build_id" --query "status" --output tsv || echo "failed")

                  # Log every 30 minutes
                  if [[ $((timeout_counter % 30)) -eq 0 ]]; then
                    echo "[INFO] Still waiting for $repo/$tag/$pipeline (build $build_id): $status ($timeout_counter/$max_timeout min)"
                  fi

                  # Break if status indicates completion or failure
                  if [[ $status == "failed" ]] || [[ $status == "cancelled" ]]; then
                    echo "[INFO] Pipeline $repo/$tag/$pipeline ended with status: $status"
                    break
                  fi
                done

                # Check for timeout and get final result
                if [[ $timeout_counter -ge $max_timeout ]]; then
                  status="timeout"
                  result="timeout"
                  echo "##vso[task.logissue type=warning]Pipeline $repo/$tag/$pipeline timed out after $max_timeout minutes"
                else
                  result=$(az pipelines runs show --id "$build_id" --query "result" --output tsv || echo "failed")
                  if [[ $result != "succeeded" ]]; then
                    echo "##vso[task.logissue type=warning]Pipeline $repo/$tag/$pipeline completed with result: $result"
                  else
                    echo "[INFO] Pipeline $repo/$tag/$pipeline completed successfully"
                  fi
                fi
                echo "$build_id;$result" > $result_file
              done
            done
            echo "Completed processing repo: $repo"
          }

          pids=()
          declare -A pid_to_repo
          for repo in $repos; do
            wait_for_jobs
            echo "Launching process for repo: $repo"
            process_repo "$repo" &
            current_pid=$!
            pids+=($current_pid)
            pid_to_repo[$current_pid]=$repo
          done

          # Wait for all background processes
          wait_counter=0
          while [ ${#pids[@]} -gt 0 ]; do
            for i in "${!pids[@]}"; do
              pid="${pids[$i]}"
              repo_name="${pid_to_repo[$pid]}"
              
              if ! kill -0 "$pid" 2>/dev/null; then
                echo "[INFO] Background process for '$repo_name' has finished"
                unset 'pids[$i]'
                unset 'pid_to_repo[$pid]'
              else
                # Log every 30 minutes (30 * 60s cycles)
                if [[ $wait_counter -gt 0 && $((wait_counter % 30)) -eq 0 ]]; then
                  echo "[INFO] Background process for '$repo_name' is still running..."
                fi
              fi
            done
            
            # Rebuild array to remove gaps
            pids=("${pids[@]}")
            
            if [ ${#pids[@]} -gt 0 ]; then
              sleep 60
              ((wait_counter++))
            fi
          done
          
          echo "All background processes completed"

          # Collect all test results (terraform + checkov) grouped by repo
          declare -A test_results_grouped
          for result_file in $result_dir/*.result; do
            [[ ! -f "$result_file" ]] && continue

            key=$(basename "$result_file" .result)
            formatted_key=$(echo "$key" | tr '#' '/')

            content=$(cat "$result_file")
            build_id=$(echo "$content" | cut -d';' -f1)
            result=$(echo "$content" | cut -d';' -f2)

            repo=$(echo "$formatted_key" | cut -d'/' -f1)
            tag=$(echo "$formatted_key" | cut -d'/' -f2)
            pipeline=$(echo "$formatted_key" | cut -d'/' -f3)
            test_results_grouped["$repo"]+='{"tag":"'$tag'","pipeline":"'$pipeline'","result":"'$result'","buildId":"'$build_id'"},'
          done

          # Save all tests data (terraform + checkov) for Jira integration (structured JSON)
          if [ ${#test_results_grouped[@]} -gt 0 ]; then
            json_output="{"
            first=true
            for repo in "${!test_results_grouped[@]}"; do
              if ! $first; then
                json_output+=","
              fi
              json_output+="\"$repo\":["
              json_output+="${test_results_grouped[$repo]%?}"  # Remove last comma
              json_output+="]"
              first=false
            done
            json_output+="}"
            echo "$json_output" | jq . > $(Pipeline.Workspace)/test_results.json
            echo "All test results (terraform + checkov) saved to test_results.json for Jira processing"
          else
            echo "[WARNING] No test results found"
          fi
    - task: PublishPipelineArtifact@1
      displayName: Publish test_results.json artifact
      condition: always()
      inputs:
        targetPath: '$(Pipeline.Workspace)/test_results.json'
        artifact: 'test_results'
        publishLocation: 'pipeline'
    - task: Bash@3
      displayName: Create/Update Jira tickets
      condition: eq(variables['Agent.JobStatus'], 'Succeeded')
      env:
        JIRA_API_TOKEN: $(JIRA_API_TOKEN)
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      inputs:
        targetType: 'inline'
        script: |
          # Jira configuration
          JIRA_BASE_URL="https://jira.otpbank.hu"
          JIRA_SEARCH_API_URL="$JIRA_BASE_URL/rest/api/2/search"
          JIRA_ISSUE_API_URL="$JIRA_BASE_URL/rest/api/2/issue"
          JIRA_TRANSITIONS_API_URL="$JIRA_BASE_URL/rest/api/2/issue"
          JIRA_PROJECT_KEY="CCE"
          # JIRA_PARENT_LABEL="test_auto_terraform_test_failure"
          JIRA_PARENT_LABEL="auto_terraform_test_failure"
          PARENT_SUMMARY="Terraform Test Failures"
          QUERY_HEADERS=(-H "Authorization: Bearer $JIRA_API_TOKEN")
          CURL_PARAMS=(-H "Content-Type: application/json" -w "%{http_code}")
          UPSERT_HEADERS=("${QUERY_HEADERS[@]}" "${CURL_PARAMS[@]}")

          test_results_file="$(Pipeline.Workspace)/test_results.json"
          if [ ! -f "$test_results_file" ]; then
            echo "[WARNING] No test results file found. Skipping Jira ticket creation."
            exit 0
          fi

          # Proceed if test_results.json exists and has content
          total_tests=$(jq '[.[] | length] | add' "$test_results_file")
          if [ "$total_tests" -eq 0 ]; then
            echo "[WARNING] No test results found. Skipping Jira ticket creation."
            exit 0
          fi

          echo "Found test results for $total_tests test runs across repos."

          echo "[INFO] Searching for existing parent ticket..."

          encoded_summary=$(echo "$PARENT_SUMMARY" | sed 's/ /+/g')
          response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_SEARCH_API_URL?jql=project=$JIRA_PROJECT_KEY+AND+labels=$JIRA_PARENT_LABEL+AND+type=Story+AND+resolution+=+Unresolved+AND+summary~%22$encoded_summary%22+ORDER+BY+created+DESC")
          if [ $? -ne 0 ] || [ -z "$response" ]; then
            echo "[WARNING] Failed to search for existing parent ticket"
          fi
          ticket_key=$(echo "$response" | jq -r '.issues[0].key // empty')
          if [ -z "$ticket_key" ]; then
            echo "[INFO] No existing parent ticket found. Creating new one..."

            payload='{"fields":{"project":{"key":"'$JIRA_PROJECT_KEY'"},"summary":"'$PARENT_SUMMARY'","description":"","issuetype":{"name":"Story"},"labels":["'$JIRA_PARENT_LABEL'"],"customfield_10101":"PP-57676"}}'
            response=$(curl -s -X POST "${UPSERT_HEADERS[@]}" -d "$payload" "$JIRA_ISSUE_API_URL")
            body=${response:0:-3}
            status=${response: -3}
            if [ "$status" = "201" ]; then
              ticket_key=$(echo "$body" | jq -r '.key')
              echo "Created parent ticket: $ticket_key"
            else
              echo "[WARNING] Failed to create parent ticket. Status: $status, Response: $body"
              exit 1
            fi
          else
            echo "Found existing parent ticket: $ticket_key"
          fi

          # Generate parent ticket description
          total_repos=$(jq -r 'keys | length' "$test_results_file")
          failed_repos=$(jq '[keys[] as $k | if (.[$k] | any(.result != "succeeded")) then 1 else empty end] | length' "$test_results_file")
          succeeded_repos=$((total_repos - failed_repos))
          parent_description="Last updated: $(date +%Y-%m-%d)\n\nTerraform Test Results Summary:\n\n"
          parent_description+="Total repositories tested: $total_repos\n"
          parent_description+="Repositories with failed tests: $failed_repos\n"
          parent_description+="Repositories with all tests succeeding: $succeeded_repos\n"
          parent_description+="Total tests run: $total_tests\n\n"
          parent_description+="Repository Details:\n"
          for repo_key in $(jq -r 'keys[]' "$test_results_file"); do
            total_tests_repo=$(jq -r ".[\"$repo_key\"] | length" "$test_results_file")
            failed_tests_repo=$(jq -r ".[\"$repo_key\"] | map(select(.result != \"succeeded\")) | length" "$test_results_file")
            succeeded_tests_repo=$((total_tests_repo - failed_tests_repo))
            parent_description+="- $repo_key: $total_tests_repo tests ($succeeded_tests_repo succeeded, $failed_tests_repo failed)\n"
          done

          # Update parent ticket description
          update_payload='{"fields":{"description":"'$parent_description'"}}'
          update_response=$(curl -s -X PUT "${UPSERT_HEADERS[@]}" -d "$update_payload" "$JIRA_ISSUE_API_URL/$ticket_key")
          update_body=${update_response:0:-3}
          update_status=${update_response: -3}
          if [ "$update_status" = "204" ]; then
            echo "[INFO] Updated parent ticket description for $ticket_key"
          else
            echo "[WARNING] Failed to update parent ticket description. Status: $update_status, Response: $update_body"
          fi

          # Get existing subtasks and their details.
          subtasks_search_response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_SEARCH_API_URL?jql=parent=$ticket_key+AND+type=Sub-task+AND+status!=Closed+ORDER+BY+created&fields=key,summary,status,assignee&maxResults=1000")
          if [ $? -ne 0 ] || [ -z "$subtasks_search_response" ]; then
            echo "[WARNING] Failed to retrieve subtasks for parent ticket $ticket_key"
          else
            echo "[DEBUG] Retrieved $(echo "$subtasks_search_response" | jq '.issues | length') subtasks"
            echo "[DEBUG] Total subtasks available: $(echo "$subtasks_search_response" | jq '.total')"
          fi

          # Archive subtasks that are 'Done' and have an assignee before regular processing.
          echo "[INFO] Checking for subtasks to archive..."
          open_subtasks_json='{"issues":[]}'
          
          while IFS= read -r subtask_json; do
            sub_key=$(echo "$subtask_json" | jq -r '.key')
            sub_status=$(echo "$subtask_json" | jq -r '.fields.status.name')
            sub_assignee=$(echo "$subtask_json" | jq -r '.fields.assignee.name // empty')
            sub_summary=$(echo "$subtask_json" | jq -r '.fields.summary')

            # Condition to archive: Status is 'Done', an assignee exists, and it's not already closed.
            if [ "$sub_status" = "Done" ] && [ -n "$sub_assignee" ]; then
              echo "[INFO] Archiving subtask $sub_key..."

              # Update summary with the closing date.
              new_summary="$sub_summary ($(date +%Y-%m-%d))"
              update_summary_payload='{"fields":{"summary":"'"$new_summary"'"}}'
              update_summary_response=$(curl -s -X PUT "${UPSERT_HEADERS[@]}" -d "$update_summary_payload" "$JIRA_ISSUE_API_URL/$sub_key")
              if [ "${update_summary_response: -3}" != "204" ]; then
                echo "[WARNING] Failed to update summary for subtask $sub_key."
              fi

              # Find the 'Closed' transition and execute it as the final step.
              transitions_response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
              close_transition_id=$(echo "$transitions_response" | jq -r '.transitions[] | select(.name == "Closed" or .to.name == "Closed") | .id' | head -1)

              if [ -n "$close_transition_id" ]; then
                close_payload='{"transition":{"id":"'"$close_transition_id"'"}}'
                close_response=$(curl -s -X POST "${UPSERT_HEADERS[@]}" -d "$close_payload" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
                if [ "${close_response: -3}" = "204" ]; then
                  echo "Subtask $sub_key successfully closed."
                else
                  echo "[WARNING] Failed to close subtask $sub_key."
                fi
              else
                echo "[WARNING] No 'Closed' transition found for subtask $sub_key."
              fi
              sleep 5
            elif [ "$sub_status" != "Closed" ]; then
              # Keep the task for regular processing if it's not to be archived and not already closed.
              open_subtasks_json=$(echo "$open_subtasks_json" | jq ".issues += [$subtask_json]")
            fi
          done < <(echo "$subtasks_search_response" | jq -c '.issues[]')
          echo "[INFO] Finished archiving process."

          # Map remaining open subtasks to repo names for subsequent processing.
          declare -A existing_subtasks_map
          while IFS= read -r subtask_line; do
            sub_key=$(echo "$subtask_line" | jq -r '.key')
            sub_summary=$(echo "$subtask_line" | jq -r '.fields.summary')
            repo_name=${sub_summary#"Terraform Tests: "}
            existing_subtasks_map["$repo_name"]="$sub_key"
          done < <(echo "$open_subtasks_json" | jq -r '.issues[] | @json')

          # Process each repository for subtask creation/update
          for repo_key in $(jq -r 'keys[]' "$test_results_file"); do
            # Generate subtask description
            sub_description="Repository: $repo_key\\n\\n"

            # Process all test results (terraform + checkov)
            sub_description+="*Test Results:*\\n"
            failed_count=0
            while IFS= read -r test_result; do
              [[ -z "$test_result" ]] && continue

              tag=$(echo "$test_result" | jq -r '.tag')
              pipeline=$(echo "$test_result" | jq -r '.pipeline')
              result=$(echo "$test_result" | jq -r '.result')
              buildId=$(echo "$test_result" | jq -r '.buildId')

              if [ "$result" != "succeeded" ]; then
                ((failed_count++))
              fi

              
              # Check if this is a checkov pipeline (contains "checkov" in name)
              if [[ "$pipeline" == *"checkov"* ]]; then
                # Checkov test: test results tab link
                pipeline_link="https://dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_build/results?buildId=$buildId&view=ms.vss-test-web.build-test-results-tab"
              else
                # Terraform test: normal build link
                pipeline_link="https://dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_build/results?buildId=$buildId"
              fi

              sub_description+="- [$pipeline|$pipeline_link] ($tag): $result\\n"
            done < <(jq -c ".[\"$repo_key\"][]" "$test_results_file")

            sub_summary="Terraform Tests: $repo_key"
            sub_key="${existing_subtasks_map["$repo_key"]}"

            if [ -z "$sub_key" ]; then
              # Create new subtask
              create_payload='{"fields":{"project":{"key":"'"$JIRA_PROJECT_KEY"'"},"parent":{"key":"'"$ticket_key"'"},"summary":"'"$sub_summary"'","description":"'"$sub_description"'","issuetype":{"name":"Sub-task"}}}'
              create_response=$(curl -s -X POST "${UPSERT_HEADERS[@]}" -d "$create_payload" "$JIRA_ISSUE_API_URL")
              body=${create_response:0:-3}
              status=${create_response: -3}
              if [ "$status" = "201" ]; then
                sub_key=$(echo "$body" | jq -r '.key')
                echo "Created subtask $sub_key for repo $repo_key"
              else
                echo "[WARNING] Failed to create subtask for repo $repo_key. Status: $status, Response: $body"
              fi
            else
              # Update description
              update_payload='{"fields":{"description":"'"$sub_description"'"}}'
              update_response=$(curl -s -X PUT "${UPSERT_HEADERS[@]}" -d "$update_payload" "$JIRA_ISSUE_API_URL/$sub_key")
              update_status=${update_response: -3}
              if [ "$update_status" = "204" ]; then
                echo "Updated subtask $sub_key description for repo $repo_key"
              else
                body=${update_response:0:-3}
                echo "[WARNING] Failed to update subtask $sub_key. Status: $update_status, Response: $body"
              fi
            fi

            # Status handling
            if [ -n "$sub_key" ]; then
              # Check current status
              issue_response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_ISSUE_API_URL/$sub_key?fields=status")
              if [ $? -ne 0 ] || [ -z "$issue_response" ]; then
                echo "[WARNING] Failed to retrieve status for subtask $sub_key"
                current_status=""
              else
                current_status=$(echo "$issue_response" | jq -r '.fields.status.name // empty')
              fi

              if [ "$failed_count" -gt 0 ] && [ "$current_status" != "To Do" ] && [ -n "$current_status" ]; then
                # Set To Do if has failures and not already To Do
                transitions_response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
                if [ $? -ne 0 ] || [ -z "$transitions_response" ]; then
                  echo "[WARNING] Failed to retrieve transitions for subtask $sub_key"
                fi
                todo_transition_id=$(echo "$transitions_response" | jq -r '.transitions[] | select(.name == "To Do" or (.to | .name == "To Do")) | .id' | head -1)
                if [ -z "$todo_transition_id" ]; then
                  echo "[WARNING] No valid transition to To Do found for subtask $sub_key in repo $repo_key"
                else
                todo_payload='{"transition":{"id":"'"$todo_transition_id"'"}}'
                todo_response=$(curl -s -X POST "${UPSERT_HEADERS[@]}" -d "$todo_payload" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
                  todo_status=${todo_response: -3}
                  if [ "$todo_status" = "204" ]; then
                    echo "Set subtask $sub_key to To Do for repo $repo_key"
                  else
                    echo "[WARNING] Failed to set subtask $sub_key to To Do for repo $repo_key. Status: $todo_status, Response: ${todo_response:0:-3}"
                  fi
                fi
              elif [ "$failed_count" -eq 0 ] && [ "$current_status" != "Done" ] && [ -n "$current_status" ]; then
                # Done if all succeeded and not already Done
                transitions_response=$(curl -s "${QUERY_HEADERS[@]}" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
                if [ $? -ne 0 ] || [ -z "$transitions_response" ]; then
                  echo "[WARNING] Failed to retrieve transitions for subtask $sub_key"
                fi
                done_transition_id=$(echo "$transitions_response" | jq -r '.transitions[] | select(.name == "Done") | .id' | head -1)
                if [ -z "$done_transition_id" ]; then
                  echo "[WARNING] No valid transition to Done found for subtask $sub_key in repo $repo_key"
                else
                done_payload='{"transition":{"id":"'"$done_transition_id"'"}}'
                done_response=$(curl -s -X POST "${UPSERT_HEADERS[@]}" -d "$done_payload" "$JIRA_TRANSITIONS_API_URL/$sub_key/transitions")
                  done_status=${done_response: -3}
                  if [ "$done_status" = "204" ]; then
                    echo "Set subtask $sub_key to Done for repo $repo_key"
                  else
                    echo "[WARNING] Failed to set subtask $sub_key to Done for repo $repo_key. Status: $todo_status, Response: ${todo_response:0:-3}"
                  fi
                fi
              fi
            fi
            sleep 5
          done
