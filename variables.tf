variable "conventions" {
  description = "terraform-conventions module"
  validation {
    condition     = length(var.conventions.project) > 0
    error_message = "(Required) Please provide value to the project in the conventions"
  }
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix"
  validation {
    condition     = can(regex("^[a-zA-Z0-9-]{2,}$", var.resource_name_suffix))
    error_message = "Can contain alphanumeric characters and hyphen and should contain at least two characters"
  }
}

variable "resource_group_name" {
  type        = string
  description = "(Required) Resource Group name"
}

variable "subnet_id" {
  type        = string
  description = "(Required) Subnet where private endpoint will be created"
}

variable "acr_umid_principal_ids" {
  type        = list(any)
  default     = []
  description = "(Optional) Principal Id list of the User Assigned Managed Identities granting AcrPull permission"
}

variable "acr_enable" {
  type        = bool
  default     = true
  description = "(Optional) Enable usage of own ACR for the cluster"
}

variable "generate_admin_token" {
  type        = bool
  default     = false
  description = "(Optional) Generate and export admin access token to key vault"
}

variable "key_vault_id" {
  type        = string
  default     = null
  description = "(Optional) Key Vault id for admin access token"
}

variable "custom_keyvault_secret_name" {
  type        = string
  default     = null
  description = "(Optional) Custom Key Vault secret name for admin access token"
}

variable "admin_token_user" {
  type        = string
  default     = "admintoken"
  description = "(Optional) Admin token name - used as username while accessing ACR"
}

variable "secret_expiration_date" {
  type        = string
  description = "(Optional) Expiration date of the Kubernetes secrets in RFC3339 format. Stored in the key vault. Maximum expiration offset time is 180 days. Only in dev it can be 365."
  default     = null
}

variable "wait_after" {
  type        = number
  default     = 0
  description = "(Optional) Seconds to wait after private endpoint created"
}

variable "enable_quarantine" {
  type        = bool
  default     = false
  description = "(Optional) Enable image quarantine mode. Note, image tagging is not possible in quarantine mode."
}

variable "network_rule_bypass_option" {
  description = "(Optional) Tells what traffic can bypass network rules. This can be AzureServices or None."
  type        = string
  default     = "None"

  validation {
    condition     = contains(["AzureServices", "None"], var.network_rule_bypass_option)
    error_message = "Invalid network rule bypass option. Allowed values are 'AzureServices' or 'None'."
  }
}

variable "identity" {
  type = object({
    type         = string
    identity_ids = optional(list(string))
  })
  default = null

  description = <<-EOT
  (Optional)An identity block supports the following:
    type - (Required) Specifies the type of Managed Service Identity that should be configured on this ACR. Possible values are SystemAssigned, UserAssigned and SystemAssigned, UserAssigned (to enable both).
    identity_ids - (Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this ACR.
  EOT
}

variable "encryption" {
  type = object({
    key_vault_key_id   = string
    identity_client_id = string
  })

  default = null

  description = <<-EOT
  The encryption block supports the following:
    key_vault_key_id - (Required) The ID of the Key Vault Key.
    identity_client_id - (Required) The client ID of the managed identity associated with the encryption key.
  EOT
}

variable "retention_policy_in_days" {
  type = number
  default = 7
  description = "(Optional) The number of days to retain and untagged manifest after which it gets purged. Defaults to 7."
}

variable "zone_redundancy_enabled" {
  description = "(Optional) Whether zone redundancy is enabled for this Container Registry? Changing this forces a new resource to be created. Defaults to false."
  type        = bool
  default     = false
}

//Diagnostics
variable "log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace"
  type        = string
  default     = null
}

variable "log_analytics_diag_logs" {
  description = "(Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "log_analytics_metrics" {
  description = "(Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}


//Health monitoring and alerts

variable "builtin_metric_monitoring" {
  description = "(Optional) Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "resource_health_monitoring" {
  description = "(Optional) Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "alert_StorageUsed_threshold" {
  description = "(Optional) Threshold for StorageUsed alert rule."
  type        = number
  default     = 483183820800 // 450GB
}

variable "resource_health_alert_location"{
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null
  }

//Tagging
variable "acre_tags" {
  description = "(Optional) Tags to apply to the Azure Container Registry"
  type        = map(string)
  default     = null
}


